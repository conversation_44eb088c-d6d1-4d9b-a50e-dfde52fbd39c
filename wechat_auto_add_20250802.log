2025-08-02 11:35:43,840 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-02 11:36:25,807 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-02 11:36:25,807 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-02 11:36:25,824 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-02 11:36:25,824 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-02 11:36:25,828 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-02 11:36:25,828 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-02 11:36:25,835 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-02 11:36:25,835 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-02 11:36:25,837 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-02 11:36:25,837 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-02 11:36:26,264 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-02 11:36:26,264 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-02 11:36:26,266 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-02 11:36:26,266 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-02 11:36:26,266 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-02 11:36:26,266 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-02 11:36:26,268 - WeChatAutoAdd - INFO - 共找到 5 个微信窗口
2025-08-02 11:36:26,268 - WeChatAutoAdd - INFO - 共找到 5 个微信窗口
2025-08-02 11:36:26,268 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-02 11:36:26,268 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-02 11:36:26,770 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-02 11:36:26,770 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-02 11:36:26,770 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-02 11:36:26,770 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-02 11:36:26,860 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-08-02 11:36:26,860 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-08-02 11:36:26,862 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-08-02 11:36:26,862 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-08-02 11:36:26,881 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250802_113626.png
2025-08-02 11:36:26,881 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250802_113626.png
2025-08-02 11:36:26,903 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-02 11:36:26,903 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-02 11:36:26,906 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-02 11:36:26,906 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-02 11:36:26,907 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-02 11:36:26,907 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-02 11:36:26,910 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-02 11:36:26,910 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-02 11:36:26,919 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-02 11:36:26,919 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-02 11:36:26,934 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250802_113626.png
2025-08-02 11:36:26,934 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250802_113626.png
2025-08-02 11:36:26,953 - WeChatAutoAdd - INFO - 底部区域原始检测到 4 个轮廓
2025-08-02 11:36:26,953 - WeChatAutoAdd - INFO - 底部区域原始检测到 4 个轮廓
2025-08-02 11:36:26,990 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(327,451), 尺寸1x1, 长宽比1.00, 面积1
2025-08-02 11:36:26,990 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(327,451), 尺寸1x1, 长宽比1.00, 面积1
2025-08-02 11:36:26,993 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-02 11:36:26,993 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-02 11:36:27,000 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-02 11:36:27,000 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-02 11:36:27,008 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-02 11:36:27,008 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-02 11:36:27,009 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-02 11:36:27,009 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-02 11:36:27,012 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-02 11:36:27,012 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-02 11:36:27,016 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-02 11:36:27,016 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-02 11:36:27,020 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-02 11:36:27,020 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-02 11:36:27,025 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-02 11:36:27,025 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-02 11:36:27,039 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250802_113627.png
2025-08-02 11:36:27,039 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250802_113627.png
2025-08-02 11:36:27,056 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-02 11:36:27,056 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-02 11:36:27,060 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-02 11:36:27,060 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-02 11:36:27,075 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250802_113627.png
2025-08-02 11:36:27,075 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250802_113627.png
2025-08-02 11:36:27,239 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-02 11:36:27,239 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-02 11:36:27,241 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-02 11:36:27,241 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-02 11:36:27,242 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-02 11:36:27,242 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-02 11:36:27,243 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-02 11:36:27,243 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-02 11:36:27,544 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-02 11:36:27,544 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-02 11:36:28,334 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-02 11:36:28,334 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-02 11:36:28,335 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-02 11:36:28,335 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-02 11:36:28,336 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-02 11:36:28,336 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-02 11:36:28,338 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-02 11:36:28,338 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-02 11:36:28,339 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-02 11:36:28,339 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-02 11:36:28,344 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-02 11:36:28,344 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-02 11:36:28,379 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-02 11:36:28,379 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-02 11:36:28,383 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 2 个
2025-08-02 11:36:28,383 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 2 个
2025-08-02 11:36:28,385 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-08-02 11:36:28,385 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-08-02 11:36:28,390 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-02 11:36:28,390 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-02 11:36:28,390 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-02 11:36:28,390 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-02 11:36:28,391 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-02 11:36:28,391 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-02 11:36:29,495 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:36:29,495 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:36:30,011 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:36:30,011 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:36:30,259 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:36:30,259 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:36:30,641 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:36:30,641 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:36:31,156 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:36:31,156 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:36:31,404 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:36:31,404 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:36:31,628 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-02 11:36:31,628 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-02 11:36:31,629 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-02 11:36:31,629 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-02 11:36:31,630 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-02 11:36:31,630 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-02 11:37:10,212 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-02 11:37:10,212 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-02 11:37:10,212 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-02 11:37:10,215 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-02 11:37:10,215 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-02 11:37:10,215 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-02 11:37:10,221 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-02 11:37:10,221 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-02 11:37:10,221 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-02 11:37:10,227 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-02 11:37:10,227 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-02 11:37:10,227 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-02 11:37:10,231 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-02 11:37:10,231 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-02 11:37:10,231 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-02 11:37:10,445 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-02 11:37:10,445 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-02 11:37:10,445 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-02 11:37:10,447 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-02 11:37:10,447 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-02 11:37:10,447 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-02 11:37:10,449 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-02 11:37:10,449 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-02 11:37:10,449 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-02 11:37:10,450 - WeChatAutoAdd - INFO - 共找到 5 个微信窗口
2025-08-02 11:37:10,450 - WeChatAutoAdd - INFO - 共找到 5 个微信窗口
2025-08-02 11:37:10,450 - WeChatAutoAdd - INFO - 共找到 5 个微信窗口
2025-08-02 11:37:10,450 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-02 11:37:10,450 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-02 11:37:10,450 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-02 11:37:10,951 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-02 11:37:10,951 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-02 11:37:10,951 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-02 11:37:10,952 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-02 11:37:10,952 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-02 11:37:10,952 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-02 11:37:11,019 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差33.94, 边缘比例0.0359
2025-08-02 11:37:11,019 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差33.94, 边缘比例0.0359
2025-08-02 11:37:11,019 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差33.94, 边缘比例0.0359
2025-08-02 11:37:11,024 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250802_113711.png
2025-08-02 11:37:11,024 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250802_113711.png
2025-08-02 11:37:11,024 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250802_113711.png
2025-08-02 11:37:11,025 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-02 11:37:11,025 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-02 11:37:11,025 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-02 11:37:11,027 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-02 11:37:11,027 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-02 11:37:11,027 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-02 11:37:11,032 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-02 11:37:11,032 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-02 11:37:11,032 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-02 11:37:11,033 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-02 11:37:11,033 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-02 11:37:11,033 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-02 11:37:11,034 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-02 11:37:11,034 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-02 11:37:11,034 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-02 11:37:11,036 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250802_113711.png
2025-08-02 11:37:11,036 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250802_113711.png
2025-08-02 11:37:11,036 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250802_113711.png
2025-08-02 11:37:11,039 - WeChatAutoAdd - INFO - 底部区域原始检测到 4 个轮廓
2025-08-02 11:37:11,039 - WeChatAutoAdd - INFO - 底部区域原始检测到 4 个轮廓
2025-08-02 11:37:11,039 - WeChatAutoAdd - INFO - 底部区域原始检测到 4 个轮廓
2025-08-02 11:37:11,040 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(327,451), 尺寸1x1, 长宽比1.00, 面积1
2025-08-02 11:37:11,040 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(327,451), 尺寸1x1, 长宽比1.00, 面积1
2025-08-02 11:37:11,040 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(327,451), 尺寸1x1, 长宽比1.00, 面积1
2025-08-02 11:37:11,041 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-02 11:37:11,041 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-02 11:37:11,041 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸3x3, 长宽比1.00, 面积9
2025-08-02 11:37:11,050 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-02 11:37:11,050 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-02 11:37:11,050 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-02 11:37:11,052 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-02 11:37:11,052 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-02 11:37:11,052 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-02 11:37:11,057 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-02 11:37:11,057 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-02 11:37:11,057 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-02 11:37:11,059 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-02 11:37:11,059 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-02 11:37:11,059 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-02 11:37:11,067 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-02 11:37:11,067 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-02 11:37:11,067 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-02 11:37:11,070 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-02 11:37:11,070 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-02 11:37:11,070 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-02 11:37:11,073 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-02 11:37:11,073 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-02 11:37:11,073 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-02 11:37:11,085 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250802_113711.png
2025-08-02 11:37:11,085 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250802_113711.png
2025-08-02 11:37:11,085 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250802_113711.png
2025-08-02 11:37:11,092 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-02 11:37:11,092 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-02 11:37:11,092 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-02 11:37:11,099 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-02 11:37:11,099 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-02 11:37:11,099 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-02 11:37:11,108 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250802_113711.png
2025-08-02 11:37:11,108 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250802_113711.png
2025-08-02 11:37:11,108 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250802_113711.png
2025-08-02 11:37:11,143 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-02 11:37:11,143 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-02 11:37:11,143 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-02 11:37:11,147 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-02 11:37:11,147 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-02 11:37:11,147 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-02 11:37:11,148 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-02 11:37:11,148 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-02 11:37:11,148 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-02 11:37:11,149 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-02 11:37:11,149 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-02 11:37:11,149 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-02 11:37:11,451 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-02 11:37:11,451 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-02 11:37:11,451 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-02 11:37:12,218 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-02 11:37:12,218 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-02 11:37:12,218 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-02 11:37:12,219 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-02 11:37:12,219 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-02 11:37:12,219 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-02 11:37:12,221 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-02 11:37:12,221 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-02 11:37:12,221 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-02 11:37:12,222 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-02 11:37:12,222 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-02 11:37:12,222 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-02 11:37:12,223 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-02 11:37:12,223 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-02 11:37:12,223 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-02 11:37:12,224 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-02 11:37:12,224 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-02 11:37:12,224 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-02 11:37:12,628 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-02 11:37:12,628 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-02 11:37:12,628 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-02 11:37:12,631 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 2 个
2025-08-02 11:37:12,631 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 2 个
2025-08-02 11:37:12,631 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 2 个
2025-08-02 11:37:12,632 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-08-02 11:37:12,632 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-08-02 11:37:12,632 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-08-02 11:37:12,634 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-02 11:37:12,634 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-02 11:37:12,634 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-02 11:37:12,639 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-02 11:37:12,639 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-02 11:37:12,639 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-02 11:37:12,642 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-02 11:37:12,642 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-02 11:37:12,642 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-02 11:37:13,583 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:37:13,583 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:37:13,583 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:37:14,302 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:37:14,302 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:37:14,302 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:37:14,553 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:37:14,553 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:37:14,553 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:37:14,800 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:37:14,800 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:37:14,800 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:37:15,665 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-02 11:37:15,665 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-02 11:37:15,665 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-02 11:37:15,666 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-02 11:37:15,666 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-02 11:37:15,666 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-02 11:37:15,667 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-02 11:37:15,667 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-02 11:37:15,667 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-02 11:38:26,722 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-02 11:38:26,722 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-02 11:38:26,722 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-02 11:38:26,722 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-02 11:38:26,727 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-02 11:38:26,727 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-02 11:38:26,727 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-02 11:38:26,727 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-02 11:38:26,731 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-02 11:38:26,731 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-02 11:38:26,731 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-02 11:38:26,731 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-02 11:38:26,739 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-02 11:38:26,739 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-02 11:38:26,739 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-02 11:38:26,739 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-02 11:38:26,743 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-02 11:38:26,743 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-02 11:38:26,743 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-02 11:38:26,743 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-02 11:38:26,745 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-02 11:38:26,745 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-02 11:38:26,745 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-02 11:38:26,745 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-02 11:38:26,749 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-02 11:38:26,749 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-02 11:38:26,749 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-02 11:38:26,749 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-02 11:38:27,421 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-02 11:38:27,421 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-02 11:38:27,421 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-02 11:38:27,421 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-02 11:38:27,425 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-02 11:38:27,425 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-02 11:38:27,425 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-02 11:38:27,425 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-02 11:38:27,427 - WeChatAutoAdd - INFO - 共找到 6 个微信窗口
2025-08-02 11:38:27,427 - WeChatAutoAdd - INFO - 共找到 6 个微信窗口
2025-08-02 11:38:27,427 - WeChatAutoAdd - INFO - 共找到 6 个微信窗口
2025-08-02 11:38:27,427 - WeChatAutoAdd - INFO - 共找到 6 个微信窗口
2025-08-02 11:38:27,428 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-02 11:38:27,428 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-02 11:38:27,428 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-02 11:38:27,428 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-02 11:38:27,930 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-02 11:38:27,930 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-02 11:38:27,930 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-02 11:38:27,930 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-02 11:38:27,931 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-02 11:38:27,931 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-02 11:38:27,931 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-02 11:38:27,931 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-02 11:38:27,995 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-08-02 11:38:27,995 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-08-02 11:38:27,995 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-08-02 11:38:27,995 - WeChatAutoAdd - WARNING - 截图可能为白色背景
2025-08-02 11:38:27,997 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-08-02 11:38:27,997 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-08-02 11:38:27,997 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-08-02 11:38:27,997 - WeChatAutoAdd - WARNING - 截图内容验证失败，可能截取了错误的窗口
2025-08-02 11:38:28,002 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250802_113827.png
2025-08-02 11:38:28,002 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250802_113827.png
2025-08-02 11:38:28,002 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250802_113827.png
2025-08-02 11:38:28,002 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250802_113827.png
2025-08-02 11:38:28,005 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-02 11:38:28,005 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-02 11:38:28,005 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-02 11:38:28,005 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-02 11:38:28,011 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-02 11:38:28,011 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-02 11:38:28,011 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-02 11:38:28,011 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-02 11:38:28,013 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-02 11:38:28,013 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-02 11:38:28,013 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-02 11:38:28,013 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-02 11:38:28,017 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-02 11:38:28,017 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-02 11:38:28,017 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-02 11:38:28,017 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-02 11:38:28,023 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-02 11:38:28,023 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-02 11:38:28,023 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-02 11:38:28,023 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-02 11:38:28,037 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250802_113828.png
2025-08-02 11:38:28,037 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250802_113828.png
2025-08-02 11:38:28,037 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250802_113828.png
2025-08-02 11:38:28,037 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250802_113828.png
2025-08-02 11:38:28,070 - WeChatAutoAdd - INFO - 底部区域原始检测到 5 个轮廓
2025-08-02 11:38:28,070 - WeChatAutoAdd - INFO - 底部区域原始检测到 5 个轮廓
2025-08-02 11:38:28,070 - WeChatAutoAdd - INFO - 底部区域原始检测到 5 个轮廓
2025-08-02 11:38:28,070 - WeChatAutoAdd - INFO - 底部区域原始检测到 5 个轮廓
2025-08-02 11:38:28,184 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(2,453), 尺寸1x1, 长宽比1.00, 面积1
2025-08-02 11:38:28,184 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(2,453), 尺寸1x1, 长宽比1.00, 面积1
2025-08-02 11:38:28,184 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(2,453), 尺寸1x1, 长宽比1.00, 面积1
2025-08-02 11:38:28,184 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(2,453), 尺寸1x1, 长宽比1.00, 面积1
2025-08-02 11:38:28,351 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(327,451), 尺寸1x1, 长宽比1.00, 面积1
2025-08-02 11:38:28,351 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(327,451), 尺寸1x1, 长宽比1.00, 面积1
2025-08-02 11:38:28,351 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(327,451), 尺寸1x1, 长宽比1.00, 面积1
2025-08-02 11:38:28,351 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(327,451), 尺寸1x1, 长宽比1.00, 面积1
2025-08-02 11:38:28,479 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸1x1, 长宽比1.00, 面积1
2025-08-02 11:38:28,479 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸1x1, 长宽比1.00, 面积1
2025-08-02 11:38:28,479 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸1x1, 长宽比1.00, 面积1
2025-08-02 11:38:28,479 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,451), 尺寸1x1, 长宽比1.00, 面积1
2025-08-02 11:38:28,533 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-02 11:38:28,533 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-02 11:38:28,533 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-02 11:38:28,533 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-02 11:38:28,539 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-02 11:38:28,539 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-02 11:38:28,539 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-02 11:38:28,539 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-02 11:38:28,548 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-02 11:38:28,548 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-02 11:38:28,548 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-02 11:38:28,548 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-02 11:38:28,581 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-02 11:38:28,581 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-02 11:38:28,581 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-02 11:38:28,581 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-02 11:38:28,647 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-02 11:38:28,647 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-02 11:38:28,647 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-02 11:38:28,647 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-02 11:38:28,661 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-02 11:38:28,661 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-02 11:38:28,661 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-02 11:38:28,661 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-02 11:38:28,680 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-02 11:38:28,680 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-02 11:38:28,680 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-02 11:38:28,680 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-02 11:38:28,688 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250802_113828.png
2025-08-02 11:38:28,688 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250802_113828.png
2025-08-02 11:38:28,688 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250802_113828.png
2025-08-02 11:38:28,688 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250802_113828.png
2025-08-02 11:38:28,693 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-02 11:38:28,693 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-02 11:38:28,693 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-02 11:38:28,693 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-02 11:38:28,695 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-02 11:38:28,695 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-02 11:38:28,695 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-02 11:38:28,695 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-02 11:38:28,699 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250802_113828.png
2025-08-02 11:38:28,699 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250802_113828.png
2025-08-02 11:38:28,699 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250802_113828.png
2025-08-02 11:38:28,699 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250802_113828.png
2025-08-02 11:38:28,732 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-02 11:38:28,732 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-02 11:38:28,732 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-02 11:38:28,732 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-02 11:38:28,735 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-02 11:38:28,735 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-02 11:38:28,735 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-02 11:38:28,735 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-02 11:38:28,737 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-02 11:38:28,737 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-02 11:38:28,737 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-02 11:38:28,737 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-02 11:38:28,745 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-02 11:38:28,745 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-02 11:38:28,745 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-02 11:38:28,745 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-02 11:38:29,054 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-02 11:38:29,054 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-02 11:38:29,054 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-02 11:38:29,054 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-02 11:38:29,826 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-02 11:38:29,826 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-02 11:38:29,826 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-02 11:38:29,826 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-02 11:38:29,828 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-02 11:38:29,828 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-02 11:38:29,828 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-02 11:38:29,828 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-02 11:38:29,830 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-02 11:38:29,830 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-02 11:38:29,830 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-02 11:38:29,830 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-02 11:38:29,834 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-02 11:38:29,834 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-02 11:38:29,834 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-02 11:38:29,834 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-02 11:38:29,836 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-02 11:38:29,836 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-02 11:38:29,836 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-02 11:38:29,836 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-02 11:38:29,838 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-02 11:38:29,838 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-02 11:38:29,838 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-02 11:38:29,838 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-02 11:38:29,851 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-02 11:38:29,851 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-02 11:38:29,851 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-02 11:38:29,851 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-02 11:38:30,232 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 2 个
2025-08-02 11:38:30,232 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 2 个
2025-08-02 11:38:30,232 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 2 个
2025-08-02 11:38:30,232 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 2 个
2025-08-02 11:38:30,235 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-08-02 11:38:30,235 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-08-02 11:38:30,235 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-08-02 11:38:30,235 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-08-02 11:38:30,237 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-02 11:38:30,237 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-02 11:38:30,237 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-02 11:38:30,237 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-02 11:38:30,242 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-02 11:38:30,242 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-02 11:38:30,242 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-02 11:38:30,242 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-02 11:38:30,246 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-02 11:38:30,246 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-02 11:38:30,246 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-02 11:38:30,246 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-02 11:38:34,225 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:38:34,225 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:38:34,225 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:38:34,225 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:38:34,226 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:38:34,226 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:38:34,226 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:38:34,226 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:38:34,453 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-02 11:38:34,453 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-02 11:38:34,453 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-02 11:38:34,453 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-02 11:38:34,454 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-02 11:38:34,454 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-02 11:38:34,454 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-02 11:38:34,454 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-02 11:38:34,457 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-02 11:38:34,457 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-02 11:38:34,457 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-02 11:38:34,457 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-02 11:40:14,748 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-02 11:40:58,882 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-02 11:40:58,882 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-02 11:40:58,886 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-02 11:40:58,886 - WeChatAutoAdd - INFO - 清理screenshots目录: 删除了 4 个图片文件
2025-08-02 11:40:58,890 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-02 11:40:58,890 - WeChatAutoAdd - INFO - 开始执行自动添加朋友操作
2025-08-02 11:40:58,894 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-02 11:40:58,894 - WeChatAutoAdd - DEBUG - 找到微信窗口: 添加朋友 - 328x454
2025-08-02 11:40:58,902 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-02 11:40:58,902 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-02 11:40:58,905 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-02 11:40:58,905 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信 - 726x650
2025-08-02 11:40:59,115 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-02 11:40:59,115 - WeChatAutoAdd - DEBUG - 找到微信窗口: 微信自动化添加好友控制台 v1.0.0 - 1216x859
2025-08-02 11:40:59,116 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-02 11:40:59,116 - WeChatAutoAdd - DEBUG - 找到微信窗口: wechat_automation_gui.py - 微信7.28 - 副本 (2) - Visual Studio Code - 1936x1048
2025-08-02 11:40:59,117 - WeChatAutoAdd - INFO - 共找到 5 个微信窗口
2025-08-02 11:40:59,117 - WeChatAutoAdd - INFO - 共找到 5 个微信窗口
2025-08-02 11:40:59,117 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-02 11:40:59,117 - WeChatAutoAdd - INFO - 找到添加朋友窗口: 添加朋友
2025-08-02 11:40:59,619 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-02 11:40:59,619 - WeChatAutoAdd - INFO - 目标窗口: 添加朋友
2025-08-02 11:40:59,619 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-02 11:40:59,619 - WeChatAutoAdd - INFO - 窗口位置: (1200, 0), 尺寸: 328x454
2025-08-02 11:40:59,695 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差33.66, 边缘比例0.0368
2025-08-02 11:40:59,695 - WeChatAutoAdd - DEBUG - 截图内容验证通过: 标准差33.66, 边缘比例0.0368
2025-08-02 11:40:59,707 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250802_114059.png
2025-08-02 11:40:59,707 - WeChatAutoAdd - DEBUG - 保存方法1截图: screenshots\window_capture_method1_20250802_114059.png
2025-08-02 11:40:59,718 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-02 11:40:59,718 - WeChatAutoAdd - INFO - 尝试使用图像处理方法查找按钮
2025-08-02 11:40:59,725 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-02 11:40:59,725 - WeChatAutoAdd - INFO - 使用改进的图像处理方法查找按钮
2025-08-02 11:40:59,727 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-02 11:40:59,727 - WeChatAutoAdd - INFO - 截图尺寸: 328x454
2025-08-02 11:40:59,728 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-02 11:40:59,728 - WeChatAutoAdd - INFO - 搜索底部245像素区域: Y=209-454 (高度:245)
2025-08-02 11:40:59,733 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-02 11:40:59,733 - WeChatAutoAdd - INFO - 特别关注区域: Y=270-330 (距底部154像素)
2025-08-02 11:40:59,738 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250802_114059.png
2025-08-02 11:40:59,738 - WeChatAutoAdd - DEBUG - 保存底部边缘检测调试图像: screenshots\bottom_edge_detection_20250802_114059.png
2025-08-02 11:40:59,741 - WeChatAutoAdd - INFO - 底部区域原始检测到 4 个轮廓
2025-08-02 11:40:59,741 - WeChatAutoAdd - INFO - 底部区域原始检测到 4 个轮廓
2025-08-02 11:40:59,741 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(1,452), 尺寸2x2, 长宽比1.00, 面积4
2025-08-02 11:40:59,741 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(1,452), 尺寸2x2, 长宽比1.00, 面积4
2025-08-02 11:40:59,742 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,450), 尺寸1x1, 长宽比1.00, 面积1
2025-08-02 11:40:59,742 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,450), 尺寸1x1, 长宽比1.00, 面积1
2025-08-02 11:40:59,743 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-02 11:40:59,743 - WeChatAutoAdd - INFO - 重要轮廓: 位置(100,236), 尺寸128x30, 长宽比4.27, 已知特征:False
2025-08-02 11:40:59,744 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-02 11:40:59,744 - WeChatAutoAdd - DEBUG - 尺寸合格但文字长度不符: 宽度128 (需要70-95)
2025-08-02 11:40:59,751 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-02 11:40:59,751 - WeChatAutoAdd - DEBUG - 底部轮廓: 位置(0,209), 尺寸328x245, 长宽比1.34, 面积80360
2025-08-02 11:40:59,753 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-02 11:40:59,753 - WeChatAutoAdd - INFO - 底部区域找到 1 个按钮候选
2025-08-02 11:40:59,756 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-02 11:40:59,756 - WeChatAutoAdd - INFO - 选择最佳候选按钮: Y=236
2025-08-02 11:40:59,759 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-02 11:40:59,759 - WeChatAutoAdd - INFO - 在底部找到按钮: (164, 251), 尺寸: 128x30, 位置得分: 1.0, 目标候选: False, 绿色按钮: False, 特殊位置: False
2025-08-02 11:40:59,768 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-02 11:40:59,768 - WeChatAutoAdd - INFO - 需要进行文字验证
2025-08-02 11:40:59,782 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250802_114059.png
2025-08-02 11:40:59,782 - WeChatAutoAdd - DEBUG - 保存改进的检测调试图片: screenshots\improved_detection_bottom_button_20250802_114059.png
2025-08-02 11:40:59,786 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-02 11:40:59,786 - WeChatAutoAdd - WARNING - 图像处理方法找到按钮，需要验证是否包含'添加到通讯录'文字
2025-08-02 11:40:59,787 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-02 11:40:59,787 - WeChatAutoAdd - INFO - 开始验证按钮区域(164, 251)是否包含'添加到通讯录'文字
2025-08-02 11:40:59,794 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250802_114059.png
2025-08-02 11:40:59,794 - WeChatAutoAdd - DEBUG - 保存按钮验证区域图像: screenshots\button_verification_region_20250802_114059.png
2025-08-02 11:40:59,866 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-02 11:40:59,866 - WeChatAutoAdd - WARNING - OCR验证失败: tesseract is not installed or it's not in your PATH. See README file for more information.，使用图像特征验证
2025-08-02 11:40:59,867 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-02 11:40:59,867 - WeChatAutoAdd - INFO - 图像特征验证: 边缘比例=0.0504, 平均亮度=243.0, 亮度标准差=38.7
2025-08-02 11:40:59,868 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-02 11:40:59,868 - WeChatAutoAdd - INFO - 图像特征验证通过: 区域可能包含文字
2025-08-02 11:40:59,868 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-02 11:40:59,868 - WeChatAutoAdd - INFO - 文字验证通过，准备执行点击操作
2025-08-02 11:41:00,169 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-02 11:41:00,169 - WeChatAutoAdd - INFO - 准备点击按钮: 窗口坐标(164, 251) -> 屏幕坐标(1364, 251)
2025-08-02 11:41:00,950 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-02 11:41:00,950 - WeChatAutoAdd - INFO - 按钮点击完成
2025-08-02 11:41:00,951 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-02 11:41:00,951 - WeChatAutoAdd - INFO - 🔍 检测是否出现频率错误对话框...
2025-08-02 11:41:00,952 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-02 11:41:00,952 - WeChatAutoAdd - INFO - ✅ 鼠标基础操作已启用（视觉反馈已禁用）
2025-08-02 11:41:00,953 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-02 11:41:00,953 - WeChatAutoAdd - INFO - ✅ 频率错误处理器初始化完成
2025-08-02 11:41:00,954 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-02 11:41:00,954 - WeChatAutoAdd - INFO - 📋 当前黑名单中有 0 个窗口
2025-08-02 11:41:00,955 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-02 11:41:00,955 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-02 11:41:00,957 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-02 11:41:00,957 - WeChatAutoAdd - DEBUG - ✅ 有效微信窗口: 微信 (726x650)
2025-08-02 11:41:01,569 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 2 个
2025-08-02 11:41:01,569 - WeChatAutoAdd - INFO - 📊 有效微信窗口统计: 2 个
2025-08-02 11:41:01,569 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-08-02 11:41:01,569 - WeChatAutoAdd - INFO - 🖥️ 检测到系统中共有 2 个微信窗口
2025-08-02 11:41:01,570 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-02 11:41:01,570 - WeChatAutoAdd - INFO - 🔍 检测是否出现'操作过于频繁'错误...
2025-08-02 11:41:01,571 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-02 11:41:01,571 - WeChatAutoAdd - INFO - 🔍 开始检测'操作过于频繁'错误...
2025-08-02 11:41:01,573 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-02 11:41:01,573 - WeChatAutoAdd - INFO - ⏱️ 检测超时时间: 3.0秒
2025-08-02 11:41:02,098 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:41:02,098 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:41:02,350 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:41:02,350 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:41:02,600 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:41:02,600 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:41:02,968 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:41:02,968 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:41:03,315 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:41:03,315 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:41:03,564 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:41:03,564 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:41:03,928 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:41:03,928 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:41:04,173 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:41:04,173 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:41:04,422 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:41:04,422 - WeChatAutoAdd - INFO - 🔍 提取到窗口文本: 添加朋友 Weixin MMUIRenderSubWindowHW...
2025-08-02 11:41:04,710 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-02 11:41:04,710 - WeChatAutoAdd - INFO - ✅ 检测完成，未发现'操作过于频繁'错误
2025-08-02 11:41:04,711 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-02 11:41:04,711 - WeChatAutoAdd - INFO - ✅ 未检测到频率错误，继续正常流程
2025-08-02 11:41:04,712 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
2025-08-02 11:41:04,712 - WeChatAutoAdd - INFO - 自动添加朋友操作执行成功
